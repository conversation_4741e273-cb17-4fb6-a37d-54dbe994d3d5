{"name": "facebook-multi-account-manager", "version": "1.0.0", "description": "برنامج إدارة حسابات المتعددة - برمجة: علي عاجل خشان المحنة", "main": "main.js", "scripts": {"start": "electron . --disable-gpu --no-sandbox", "build": "electron-builder", "dev": "electron . --dev --disable-gpu --no-sandbox", "test": "electron . --disable-gpu --no-sandbox --disable-web-security"}, "keywords": ["facebook", "multi-account", "automation", "social-media"], "author": "علي عاجل خشان المحنة <<EMAIL>>", "license": "MIT", "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4"}, "dependencies": {"sqlite3": "^5.1.6", "playwright": "^1.40.0", "node-cron": "^3.0.3", "bcrypt": "^5.1.1"}, "build": {"appId": "com.aliajil.facebook-manager", "productName": "Facebook Multi-Account Manager", "directories": {"output": "dist"}, "files": ["**/*", "!node_modules", "!dist"], "win": {"target": "nsis", "icon": "assets/icon.ico"}}}