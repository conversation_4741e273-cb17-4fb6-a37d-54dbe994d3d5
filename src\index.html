<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Facebook Multi-Account Manager</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/animations.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- شريط العنوان المخصص -->
    <div class="custom-titlebar">
        <div class="titlebar-left">
            <div class="app-logo">
                <i class="fab fa-facebook-f"></i>
                <span>Facebook Multi-Account Manager</span>
            </div>
        </div>
        <div class="titlebar-right">
            <button class="titlebar-btn minimize-btn" id="minimizeBtn">
                <i class="fas fa-window-minimize"></i>
            </button>
            <button class="titlebar-btn maximize-btn" id="maximizeBtn">
                <i class="fas fa-window-maximize"></i>
            </button>
            <button class="titlebar-btn close-btn" id="closeBtn">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>

    <!-- الحاوية الرئيسية -->
    <div class="main-container">
        <!-- الشريط الجانبي -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user-circle"></i>
                    </div>
                    <div class="user-details">
                        <h3>مرحباً بك</h3>
                        <p>مدير الحسابات</p>
                    </div>
                </div>
            </div>

            <ul class="nav-menu">
                <li class="nav-item active" data-tab="dashboard">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>لوحة التحكم</span>
                </li>
                <li class="nav-item" data-tab="accounts">
                    <i class="fas fa-users"></i>
                    <span>إدارة الحسابات</span>
                </li>
                <li class="nav-item" data-tab="posting">
                    <i class="fas fa-edit"></i>
                    <span>النشر الجماعي</span>
                </li>
                <li class="nav-item" data-tab="comments">
                    <i class="fas fa-comments"></i>
                    <span>التعليقات الجماعية</span>
                </li>
                <li class="nav-item" data-tab="friends">
                    <i class="fas fa-user-plus"></i>
                    <span>طلبات الصداقة</span>
                </li>
                <li class="nav-item" data-tab="displays">
                    <i class="fas fa-desktop"></i>
                    <span>شاشات العرض</span>
                </li>
                <li class="nav-item" data-tab="replies">
                    <i class="fas fa-reply"></i>
                    <span>الردود الجاهزة</span>
                </li>
                <li class="nav-item" data-tab="logs">
                    <i class="fas fa-list"></i>
                    <span>سجل النشاطات</span>
                </li>
                <li class="nav-item" data-tab="settings">
                    <i class="fas fa-cog"></i>
                    <span>الإعدادات</span>
                </li>
                <li class="nav-item" data-tab="about">
                    <i class="fas fa-info-circle"></i>
                    <span>حول البرنامج</span>
                </li>
            </ul>

            <!-- حقوق المطور -->
            <div class="developer-signature">
                <p>تطوير: <strong>علي عاجل خشان المحنة</strong></p>
            </div>
        </nav>

        <!-- المحتوى الرئيسي -->
        <main class="main-content">
            <!-- تبويب لوحة التحكم -->
            <div class="tab-content active" id="dashboard">
                <div class="content-header">
                    <h1><i class="fas fa-tachometer-alt"></i> لوحة التحكم</h1>
                    <p>نظرة عامة على حالة البرنامج والحسابات</p>
                </div>

                <div class="dashboard-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalAccounts">0</h3>
                            <p>إجمالي الحسابات</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon active">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="activeAccounts">0</h3>
                            <p>الحسابات النشطة</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-paper-plane"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalPosts">0</h3>
                            <p>المنشورات اليوم</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-tasks"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="runningTasks">0</h3>
                            <p>المهام الجارية</p>
                        </div>
                    </div>
                </div>

                <div class="dashboard-sections">
                    <div class="recent-activity">
                        <h3><i class="fas fa-clock"></i> النشاطات الأخيرة</h3>
                        <div class="activity-list" id="recentActivity">
                            <p class="no-data">لا توجد نشاطات حديثة</p>
                        </div>
                    </div>

                    <div class="system-status">
                        <h3><i class="fas fa-heartbeat"></i> حالة النظام</h3>
                        <div class="status-items">
                            <div class="status-item">
                                <span class="status-label">قاعدة البيانات</span>
                                <span class="status-value connected">متصلة</span>
                            </div>
                            <div class="status-item">
                                <span class="status-label">الذاكرة المستخدمة</span>
                                <span class="status-value" id="memoryUsage">-- MB</span>
                            </div>
                            <div class="status-item">
                                <span class="status-label">وقت التشغيل</span>
                                <span class="status-value" id="uptime">--</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تبويب إدارة الحسابات -->
            <div class="tab-content" id="accounts">
                <div class="content-header">
                    <h1><i class="fas fa-users"></i> إدارة الحسابات</h1>
                    <button class="btn btn-primary" id="addAccountBtn">
                        <i class="fas fa-plus"></i> إضافة حساب جديد
                    </button>
                </div>

                <div class="accounts-container">
                    <div class="accounts-table">
                        <table>
                            <thead>
                                <tr>
                                    <th>الصورة</th>
                                    <th>اسم الحساب</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>الحالة</th>
                                    <th>آخر نشاط</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="accountsTableBody">
                                <tr class="no-data-row">
                                    <td colspan="6">لا توجد حسابات مضافة</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- تبويب النشر الجماعي -->
            <div class="tab-content" id="posting">
                <div class="content-header">
                    <h1><i class="fas fa-edit"></i> النشر الجماعي</h1>
                    <p>نشر محتوى موحد عبر حسابات متعددة</p>
                </div>

                <div class="posting-form">
                    <div class="form-section">
                        <h3><i class="fas fa-pencil-alt"></i> المحتوى والوسائط</h3>
                        <div class="form-group">
                            <label>محتوى المنشور</label>
                            <textarea id="postContent" placeholder="اكتب محتوى المنشور هنا..."></textarea>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label>إضافة صور/فيديوهات</label>
                                <div class="file-upload-area" id="mediaUpload">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                    <p>اسحب الملفات هنا أو انقر للاختيار</p>
                                    <input type="file" id="mediaFiles" multiple accept="image/*,video/*">
                                </div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="publicPost">
                                    نشر عام على الصفحة الشخصية
                                </label>
                            </div>
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="storyPost">
                                    نشر كستوري
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h3><i class="fas fa-bullseye"></i> استهداف المجموعات</h3>
                        <div class="form-group">
                            <label>روابط المجموعات المستهدفة</label>
                            <textarea id="targetGroups" placeholder="ضع رابط كل مجموعة في سطر منفصل..."></textarea>
                        </div>
                        <button class="btn btn-secondary" id="loadGroupsBtn">
                            <i class="fas fa-upload"></i> تحميل من ملف
                        </button>
                    </div>

                    <div class="form-section">
                        <h3><i class="fas fa-cogs"></i> إعدادات النشر</h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label>ترتيب الحسابات</label>
                                <select id="accountOrder">
                                    <option value="cyclic">دوري (Cyclic)</option>
                                    <option value="sequential">متسلسل (Sequential)</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>المنشورات لكل حساب</label>
                                <input type="number" id="postsPerAccount" value="1" min="1" max="10">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label>الحد الأدنى للتأخير (دقائق)</label>
                                <input type="number" id="minDelay" value="1" min="0" max="60">
                            </div>
                            <div class="form-group">
                                <label>الحد الأقصى للتأخير (دقائق)</label>
                                <input type="number" id="maxDelay" value="5" min="1" max="120">
                            </div>
                        </div>

                        <div class="form-actions">
                            <button class="btn btn-success" id="startPostingBtn">
                                <i class="fas fa-play"></i> بدء النشر
                            </button>
                            <button class="btn btn-warning" id="pausePostingBtn" disabled>
                                <i class="fas fa-pause"></i> إيقاف مؤقت
                            </button>
                            <button class="btn btn-danger" id="stopPostingBtn" disabled>
                                <i class="fas fa-stop"></i> إيقاف
                            </button>
                        </div>

                        <div class="progress-container" id="postingProgress" style="display: none;">
                            <div class="progress-bar">
                                <div class="progress-fill" id="postingProgressFill"></div>
                            </div>
                            <div class="progress-text" id="postingProgressText">0%</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- باقي التبويبات سيتم إضافتها تدريجياً -->
            <div class="tab-content" id="comments">
                <div class="content-header">
                    <h1><i class="fas fa-comments"></i> التعليقات الجماعية</h1>
                    <p>إضافة تعليقات موحدة على منشورات محددة</p>
                </div>
                <div class="coming-soon">
                    <i class="fas fa-wrench"></i>
                    <h3>قيد التطوير</h3>
                    <p>هذه الميزة ستكون متاحة قريباً</p>
                </div>
            </div>

            <div class="tab-content" id="friends">
                <div class="content-header">
                    <h1><i class="fas fa-user-plus"></i> طلبات الصداقة</h1>
                    <p>إرسال طلبات صداقة جماعية</p>
                </div>
                <div class="coming-soon">
                    <i class="fas fa-wrench"></i>
                    <h3>قيد التطوير</h3>
                    <p>هذه الميزة ستكون متاحة قريباً</p>
                </div>
            </div>

            <div class="tab-content" id="displays">
                <div class="content-header">
                    <h1><i class="fas fa-desktop"></i> شاشات العرض</h1>
                    <p>عرض موحد لبيانات الحسابات</p>
                </div>
                <div class="coming-soon">
                    <i class="fas fa-wrench"></i>
                    <h3>قيد التطوير</h3>
                    <p>هذه الميزة ستكون متاحة قريباً</p>
                </div>
            </div>

            <div class="tab-content" id="replies">
                <div class="content-header">
                    <h1><i class="fas fa-reply"></i> الردود الجاهزة</h1>
                    <p>إدارة مكتبة الردود والتعليقات</p>
                </div>
                <div class="coming-soon">
                    <i class="fas fa-wrench"></i>
                    <h3>قيد التطوير</h3>
                    <p>هذه الميزة ستكون متاحة قريباً</p>
                </div>
            </div>

            <div class="tab-content" id="logs">
                <div class="content-header">
                    <h1><i class="fas fa-list"></i> سجل النشاطات</h1>
                    <p>تتبع جميع العمليات والأنشطة</p>
                </div>
                <div class="coming-soon">
                    <i class="fas fa-wrench"></i>
                    <h3>قيد التطوير</h3>
                    <p>هذه الميزة ستكون متاحة قريباً</p>
                </div>
            </div>

            <div class="tab-content" id="settings">
                <div class="content-header">
                    <h1><i class="fas fa-cog"></i> الإعدادات</h1>
                    <p>إعدادات البرنامج العامة</p>
                </div>
                <div class="coming-soon">
                    <i class="fas fa-wrench"></i>
                    <h3>قيد التطوير</h3>
                    <p>هذه الميزة ستكون متاحة قريباً</p>
                </div>
            </div>

            <!-- تبويب حول البرنامج -->
            <div class="tab-content" id="about">
                <div class="content-header">
                    <h1><i class="fas fa-info-circle"></i> حول البرنامج</h1>
                </div>

                <div class="about-container">
                    <div class="about-card">
                        <div class="about-header">
                            <div class="app-icon">
                                <i class="fab fa-facebook-f"></i>
                            </div>
                            <div class="app-info">
                                <h2>Facebook Multi-Account Manager</h2>
                                <p>الإصدار 1.0.0</p>
                            </div>
                        </div>

                        <div class="about-content">
                            <h3>حول البرنامج</h3>
                            <p>برنامج متطور لإدارة حسابات فيسبوك المتعددة بشكل موحد ومؤتمت، يوفر أدوات قوية للنشر الجماعي وإدارة التفاعلات.</p>

                            <h3>المطور</h3>
                            <div class="developer-info">
                                <div class="developer-avatar">
                                    <i class="fas fa-user-tie"></i>
                                </div>
                                <div class="developer-details">
                                    <h4>علي عاجل خشان المحنة</h4>
                                    <p class="developer-title">مطور برمجيات - خبير في JavaScript</p>
                                    <p class="developer-description">
                                        مطور محترف متخصص في تطوير تطبيقات سطح المكتب والويب باستخدام JavaScript وتقنيات حديثة. 
                                        لديه خبرة واسعة في بناء أنظمة إدارة المحتوى والتطبيقات التفاعلية.
                                    </p>
                                    
                                    <div class="contact-info">
                                        <div class="contact-item">
                                            <i class="fas fa-envelope"></i>
                                            <span><EMAIL></span>
                                        </div>
                                        <div class="contact-item">
                                            <i class="fas fa-phone"></i>
                                            <span>07727232639</span>
                                        </div>
                                    </div>

                                    <div class="skills">
                                        <h5>المهارات التقنية</h5>
                                        <div class="skill-tags">
                                            <span class="skill-tag">JavaScript</span>
                                            <span class="skill-tag">Node.js</span>
                                            <span class="skill-tag">Electron</span>
                                            <span class="skill-tag">HTML5/CSS3</span>
                                            <span class="skill-tag">Database Design</span>
                                            <span class="skill-tag">Web Automation</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="app-features">
                                <h3>مميزات البرنامج</h3>
                                <ul>
                                    <li><i class="fas fa-check"></i> إدارة حسابات متعددة بشكل آمن</li>
                                    <li><i class="fas fa-check"></i> النشر الجماعي المتطور</li>
                                    <li><i class="fas fa-check"></i> تشفير بيانات الحسابات</li>
                                    <li><i class="fas fa-check"></i> واجهة مستخدم عربية جذابة</li>
                                    <li><i class="fas fa-check"></i> تقارير مفصلة للأنشطة</li>
                                    <li><i class="fas fa-check"></i> جدولة المهام المتقدمة</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- نافذة إضافة حساب -->
    <div class="modal" id="addAccountModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-user-plus"></i> إضافة حساب جديد</h3>
                <button class="modal-close" id="closeAddAccountModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="addAccountForm">
                    <div class="form-group">
                        <label>اسم المستخدم أو البريد الإلكتروني</label>
                        <input type="text" id="accountEmail" required>
                    </div>
                    <div class="form-group">
                        <label>كلمة المرور</label>
                        <div class="password-input">
                            <input type="password" id="accountPassword" required>
                            <button type="button" class="toggle-password">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>اسم مستعار (اختياري)</label>
                        <input type="text" id="accountNickname" placeholder="اسم للتعرف على الحساب">
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" id="testConnectionBtn">
                            <i class="fas fa-link"></i> اختبار الاتصال
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ الحساب
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="js/utils.js"></script>
    <script src="js/database.js"></script>
    <script src="js/accounts.js"></script>
    <script src="js/posting.js"></script>
    <script src="js/app.js"></script>
</body>
</html>