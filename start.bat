@echo off
echo تشغيل برنامج Facebook Multi-Account Manager
echo تطوير: علي عاجل خشان المحنة
echo.

REM التحقق من وجود Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Node.js غير مثبت على النظام
    echo يرجى تثبيت Node.js من https://nodejs.org
    pause
    exit
)

REM التحقق من وجود npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: npm غير متوفر
    pause
    exit
)

REM التحقق من وجود مجلد node_modules
if not exist "node_modules" (
    echo تثبيت التبعيات...
    npm install
)

REM تشغيل البرنامج
echo تشغيل البرنامج...
npm start

pause